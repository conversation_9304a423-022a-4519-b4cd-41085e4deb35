# Example configuration file for Desktop Automation Agent
# Copy this to config.yaml and customize as needed

# Agent configuration
agent:
  # Gemini model to use
  model_name: "gemini-2.5-flash"
  
  # Model parameters
  temperature: 0.1          # Lower = more deterministic
  max_tokens: 8192          # Maximum response length
  memory_window: 10         # Number of conversation turns to remember
  
  # Agent behavior
  verbose: true             # Enable detailed logging
  max_iterations: 20        # Maximum planning iterations
  max_execution_time: 300   # Timeout in seconds (5 minutes)

# Logging configuration
log_level: "INFO"           # DEBUG, INFO, WARNING, ERROR

# Screenshot settings
screenshot:
  quality: 95               # JPEG quality (1-100)
  format: "PNG"             # PNG or JPEG
  default_path: "screenshots/"

# Desktop automation settings
desktop:
  # PyAutoGUI settings
  pause: 0.1                # Pause between actions
  failsafe: true            # Enable failsafe (move to corner to stop)
  
  # Mouse settings
  mouse_duration: 0.5       # Default mouse movement duration
  click_interval: 0.1       # Interval between multiple clicks
  
  # Keyboard settings
  type_interval: 0.05       # Interval between keystrokes
  key_press_interval: 0.1   # Interval between key presses

# Browser automation settings
browser:
  # Default browser type
  browser_type: "chromium"  # chromium, firefox, webkit
  
  # Browser launch options
  headless: false           # Run browser in headless mode
  slow_mo: 0                # Slow down operations (ms)
  
  # Timeouts (in milliseconds)
  default_timeout: 30000    # 30 seconds
  navigation_timeout: 60000 # 1 minute
  
  # Viewport settings
  viewport:
    width: 1280
    height: 720
  
  # Additional launch args
  args:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"

# Safety settings
safety:
  # Confirmation prompts
  confirm_destructive_actions: false
  confirm_file_operations: false
  
  # Action limits
  max_mouse_clicks_per_minute: 60
  max_keystrokes_per_minute: 300
  
  # Restricted areas (coordinates to avoid)
  restricted_areas: []
  
  # Allowed domains for browser automation
  allowed_domains: []       # Empty = all domains allowed

# Performance settings
performance:
  # Delays between actions
  action_delay: 0.1         # Seconds
  screenshot_delay: 0.5     # Seconds
  
  # Retry settings
  max_retries: 3
  retry_delay: 1.0          # Seconds
  
  # Memory management
  clear_memory_after: 100   # Clear after N interactions

# Custom tool settings
custom_tools:
  # Enable/disable specific tools
  enable_file_operations: true
  enable_system_commands: false
  enable_network_requests: true
  
  # Custom tool paths
  tool_directories: []

# Integration settings
integrations:
  # External services
  enable_ocr: false         # Optical Character Recognition
  enable_speech: false      # Text-to-speech
  
  # API endpoints
  custom_apis: {}

# Development settings
development:
  # Debug options
  save_intermediate_screenshots: false
  log_all_actions: true
  
  # Testing
  dry_run: false            # Don't actually perform actions
  mock_external_calls: false
