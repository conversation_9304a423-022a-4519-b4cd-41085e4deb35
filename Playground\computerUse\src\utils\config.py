"""
Configuration management utilities.
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load configuration from file or environment variables.
    
    Args:
        config_path: Path to configuration file (JSON or YAML)
        
    Returns:
        Configuration dictionary
    """
    config = {
        "agent": {
            "model_name": "gemini-2.5-flash",
            "temperature": 0.1,
            "max_tokens": 8192,
            "memory_window": 10,
            "verbose": True
        },
        "log_level": "INFO",
        "screenshot": {
            "quality": 95,
            "format": "PNG"
        }
    }
    
    # Load from file if provided
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                if config_path.endswith('.json'):
                    file_config = json.load(f)
                elif config_path.endswith(('.yml', '.yaml')):
                    file_config = yaml.safe_load(f)
                else:
                    raise ValueError("Config file must be <PERSON>SO<PERSON> or YAM<PERSON>")
            
            # Merge with default config
            config.update(file_config)
            
        except Exception as e:
            logging.warning(f"Failed to load config file {config_path}: {e}")
    
    # Override with environment variables
    if os.getenv("GOOGLE_API_KEY"):
        config["agent"]["api_key"] = os.getenv("GOOGLE_API_KEY")
    
    if os.getenv("LOG_LEVEL"):
        config["log_level"] = os.getenv("LOG_LEVEL")
    
    if os.getenv("SCREENSHOT_QUALITY"):
        config["screenshot"]["quality"] = int(os.getenv("SCREENSHOT_QUALITY"))
    
    if os.getenv("SCREENSHOT_FORMAT"):
        config["screenshot"]["format"] = os.getenv("SCREENSHOT_FORMAT")
    
    return config


def setup_logging(level: str = "INFO"):
    """
    Setup logging configuration.
    
    Args:
        level: Logging level
    """
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('automation.log')
        ]
    )
    
    # Reduce noise from some libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('playwright').setLevel(logging.WARNING)


def save_config(config: Dict[str, Any], config_path: str):
    """
    Save configuration to file.
    
    Args:
        config: Configuration dictionary
        config_path: Path to save configuration
    """
    try:
        with open(config_path, 'w') as f:
            if config_path.endswith('.json'):
                json.dump(config, f, indent=2)
            elif config_path.endswith(('.yml', '.yaml')):
                yaml.dump(config, f, default_flow_style=False)
            else:
                raise ValueError("Config file must be JSON or YAML")
        
        logging.info(f"Configuration saved to {config_path}")
        
    except Exception as e:
        logging.error(f"Failed to save config: {e}")
        raise
