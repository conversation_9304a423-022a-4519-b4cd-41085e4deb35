# Desktop Automation Agent

A comprehensive desktop and browser automation system powered by **Gemini 2.5 Flash**, **LangChain agents**, and **Playwright**. This system enables natural language control of both desktop applications and web browsers through intelligent AI planning and execution.

## 🚀 Features

### Desktop Automation
- **Mouse Control**: Precise mouse movements, clicks, drags, and scrolling
- **Keyboard Input**: Text typing, key combinations, and shortcuts
- **Screen Capture**: Screenshots of full screen or specific regions
- **Coordinate-based Actions**: Pixel-perfect positioning and interaction
- **Application Control**: Window switching, focus management

### Browser Automation
- **Web Navigation**: URL navigation with smart waiting
- **Element Interaction**: CSS selector-based clicking and typing
- **Form Handling**: Input field management and form submission
- **JavaScript Execution**: Custom script execution in browser context
- **Page Analysis**: Text extraction and element detection

### AI-Powered Planning
- **Natural Language Interface**: Describe tasks in plain English
- **Intelligent Planning**: Multi-step task breakdown and execution
- **Context Awareness**: Memory of previous actions and state
- **Error Recovery**: Automatic retry and alternative approaches
- **Tool Selection**: Smart choice between desktop and browser tools

## 📦 Installation

### Prerequisites
- Python 3.8 or higher
- Google API key for Gemini 2.5 Flash
- Windows, macOS, or Linux

### Setup

1. **Clone and navigate to the project:**
   ```bash
   cd Playground/computerUse
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers:**
   ```bash
   playwright install
   ```

4. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env and add your GOOGLE_API_KEY
   ```

5. **Install the package (optional):**
   ```bash
   pip install -e .
   ```

## 🎯 Quick Start

### Interactive Mode
```bash
python -m src.main
```

### Single Task Execution
```bash
python -m src.main --task "Take a screenshot and open Google Chrome"
```

### Batch Mode
```bash
python -m src.main --batch examples/tasks.txt
```

### Agent Types
```bash
# Hybrid agent (desktop + browser)
python -m src.main --agent-type hybrid

# Desktop-only agent
python -m src.main --agent-type desktop

# Browser-only agent
python -m src.main --agent-type browser
```

## 💡 Usage Examples

### Basic Desktop Tasks
```python
from src.agents.gemini_agent import SpecializedAgent

# Create desktop agent
agent = SpecializedAgent.create_desktop_agent()

# Execute tasks
agent.execute_task("Take a screenshot of the current screen")
agent.execute_task("Move mouse to coordinates 500, 300 and click")
agent.execute_task("Type 'Hello World!' and press Enter")
agent.execute_task("Press Ctrl+C to copy selected text")
```

### Browser Automation
```python
# Create browser agent
agent = SpecializedAgent.create_browser_agent()

# Web automation tasks
agent.execute_task("Navigate to https://www.google.com")
agent.execute_task("Click on the search box and type 'AI automation'")
agent.execute_task("Press Enter and wait for results")
agent.execute_task("Take a screenshot of the search results")
```

### Complex Workflows
```python
# Create hybrid agent
agent = SpecializedAgent.create_hybrid_agent()

# Multi-step workflow
workflow = """
Research a topic online and create notes:
1. Open a browser and go to Wikipedia
2. Search for 'Machine Learning'
3. Read the first paragraph
4. Open a text editor
5. Write a summary of what you learned
6. Save the file as 'ml_notes.txt'
"""

agent.execute_task(workflow)
```

## 🔧 Available Tools

### Desktop Tools
- `move_mouse(x, y)` - Move mouse to coordinates
- `click_mouse(x, y, button)` - Click at position
- `type_text(text)` - Type text using keyboard
- `press_key(key)` - Press keys or combinations
- `take_screenshot()` - Capture screen
- `scroll(clicks, x, y)` - Scroll at position

### Browser Tools
- `navigate_to_url(url)` - Navigate to webpage
- `click_element(selector)` - Click element by CSS selector
- `type_in_element(selector, text)` - Type in form fields
- `wait_for_element(selector)` - Wait for element to appear
- `get_element_text(selector)` - Extract text from element
- `execute_javascript(script)` - Run JavaScript code

## 🎮 Interactive Commands

When running in interactive mode, you can use these commands:

- `help` - Show available commands
- `status` - Show agent status
- `tools` - List available tools
- `memory` - Show conversation history
- `clear` - Clear conversation memory
- `init <type>` - Initialize agent (hybrid/desktop/browser)
- `quit` - Exit the application

## 📝 Configuration

Create a `config.yaml` file to customize settings:

```yaml
agent:
  model_name: "gemini-2.5-flash"
  temperature: 0.1
  max_tokens: 8192
  memory_window: 10
  verbose: true

log_level: "INFO"

screenshot:
  quality: 95
  format: "PNG"
```

## 🔒 Safety Features

- **Failsafe Mode**: PyAutoGUI failsafe (move mouse to corner to stop)
- **Timeouts**: Configurable timeouts for all operations
- **Error Handling**: Graceful error recovery and reporting
- **Confirmation Prompts**: Optional confirmation for destructive actions
- **Logging**: Comprehensive logging of all actions

## 📚 Examples

Check the `examples/` directory for:
- `basic_usage.py` - Basic automation examples
- `tasks.txt` - Sample batch tasks file
- More advanced examples and use cases

## 🛠️ Development

### Project Structure
```
src/
├── agents/          # AI agent implementations
├── tools/           # Automation tool implementations
├── utils/           # Utility functions
└── main.py          # Main application entry point

examples/            # Usage examples
tests/              # Test files
```

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black src/
flake8 src/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This tool can control your computer and browser. Use responsibly and be aware of:
- Potential security implications
- The need for proper API key management
- Testing in safe environments first
- Understanding the actions being performed

## 🆘 Troubleshooting

### Common Issues

1. **API Key Error**: Make sure `GOOGLE_API_KEY` is set in your environment
2. **Permission Errors**: Run with appropriate permissions for screen capture
3. **Browser Issues**: Ensure Playwright browsers are installed
4. **Import Errors**: Check that all dependencies are installed

### Getting Help

- Check the examples in the `examples/` directory
- Review the logs in `automation.log`
- Use the `--verbose` flag for detailed output
- Open an issue on GitHub for bugs or feature requests

## 🎉 Acknowledgments

- **Google Gemini** for the powerful language model
- **LangChain** for the agent framework
- **Playwright** for browser automation
- **PyAutoGUI** for desktop automation
- The open-source community for inspiration and tools
