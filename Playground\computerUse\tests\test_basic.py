"""
Basic tests for the Desktop Automation Agent.
"""

import pytest
import os
import sys
from unittest.mock import Mock, patch

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from tools.desktop_automation import DesktopAutomation
from tools.langchain_tools import MouseMoveTool, TypeTextTool
from utils.config import load_config


class TestDesktopAutomation:
    """Test desktop automation functionality."""
    
    def test_desktop_automation_init(self):
        """Test desktop automation initialization."""
        desktop = DesktopAutomation()
        assert desktop is not None
        assert hasattr(desktop, 'screen_capture')
    
    def test_get_screen_size(self):
        """Test getting screen size."""
        desktop = DesktopAutomation()
        size = desktop.get_screen_size()
        assert isinstance(size, tuple)
        assert len(size) == 2
        assert size[0] > 0 and size[1] > 0
    
    @patch('pyautogui.moveTo')
    def test_move_mouse(self, mock_move):
        """Test mouse movement."""
        desktop = DesktopAutomation()
        result = desktop.move_mouse(100, 200)
        assert result is True
        mock_move.assert_called_once_with(100, 200, duration=0.5)
    
    @patch('pyautogui.click')
    def test_click(self, mock_click):
        """Test mouse clicking."""
        desktop = DesktopAutomation()
        result = desktop.click(100, 200)
        assert result is True
        mock_click.assert_called_once()
    
    @patch('pyautogui.write')
    def test_type_text(self, mock_write):
        """Test text typing."""
        desktop = DesktopAutomation()
        result = desktop.type_text("Hello World")
        assert result is True
        mock_write.assert_called_once_with("Hello World", interval=0.05)


class TestLangChainTools:
    """Test LangChain tool wrappers."""
    
    def test_mouse_move_tool_init(self):
        """Test mouse move tool initialization."""
        tool = MouseMoveTool()
        assert tool.name == "move_mouse"
        assert "move mouse cursor" in tool.description.lower()
    
    def test_type_text_tool_init(self):
        """Test type text tool initialization."""
        tool = TypeTextTool()
        assert tool.name == "type_text"
        assert "type text" in tool.description.lower()
    
    @patch('src.tools.desktop_automation.DesktopAutomation.move_mouse')
    def test_mouse_move_tool_run(self, mock_move):
        """Test mouse move tool execution."""
        mock_move.return_value = True
        tool = MouseMoveTool()
        result = tool._run(100, 200)
        assert "Success" in result
        mock_move.assert_called_once_with(100, 200, 0.5)
    
    @patch('src.tools.desktop_automation.DesktopAutomation.type_text')
    def test_type_text_tool_run(self, mock_type):
        """Test type text tool execution."""
        mock_type.return_value = True
        tool = TypeTextTool()
        result = tool._run("Hello")
        assert "Success" in result
        mock_type.assert_called_once_with("Hello", 0.05)


class TestConfig:
    """Test configuration management."""
    
    def test_load_default_config(self):
        """Test loading default configuration."""
        config = load_config()
        assert isinstance(config, dict)
        assert "agent" in config
        assert "log_level" in config
        assert config["agent"]["model_name"] == "gemini-2.5-flash"
    
    def test_config_with_env_vars(self):
        """Test configuration with environment variables."""
        with patch.dict(os.environ, {"LOG_LEVEL": "DEBUG"}):
            config = load_config()
            assert config["log_level"] == "DEBUG"


class TestIntegration:
    """Integration tests."""
    
    @pytest.mark.skipif(not os.getenv("GOOGLE_API_KEY"), reason="No API key")
    def test_agent_initialization(self):
        """Test agent initialization with real API key."""
        from agents.gemini_agent import SpecializedAgent
        
        try:
            agent = SpecializedAgent.create_desktop_agent(verbose=False)
            assert agent is not None
            assert len(agent.tools) > 0
        except Exception as e:
            pytest.skip(f"Agent initialization failed: {e}")
    
    def test_tool_availability(self):
        """Test that all expected tools are available."""
        from tools.langchain_tools import get_all_tools, get_desktop_tools, get_browser_tools
        
        all_tools = get_all_tools()
        desktop_tools = get_desktop_tools()
        browser_tools = get_browser_tools()
        
        assert len(all_tools) > 0
        assert len(desktop_tools) > 0
        assert len(browser_tools) > 0
        assert len(all_tools) == len(desktop_tools) + len(browser_tools)
        
        # Check specific tools exist
        tool_names = [tool.name for tool in all_tools]
        expected_tools = [
            "move_mouse", "click_mouse", "type_text", "press_key",
            "navigate_to_url", "click_element", "type_in_element"
        ]
        
        for expected_tool in expected_tools:
            assert expected_tool in tool_names


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
