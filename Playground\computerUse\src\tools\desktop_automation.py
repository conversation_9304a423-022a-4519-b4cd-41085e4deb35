"""
Desktop automation tools for mouse, keyboard, and screen interactions.
"""

import time
import pyautogui
import mss
import cv2
import numpy as np
from PIL import Image
from typing import Tuple, Optional, List, Dict, Any
from pynput import keyboard, mouse
from pynput.keyboard import Key
import logging

# Configure pyautogui safety
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.1

logger = logging.getLogger(__name__)


class DesktopAutomation:
    """Core desktop automation functionality."""
    
    def __init__(self):
        self.screen_capture = mss.mss()
        
    def get_screen_size(self) -> Tuple[int, int]:
        """Get the screen resolution."""
        return pyautogui.size()
    
    def take_screenshot(self, region: Optional[Dict[str, int]] = None) -> np.ndarray:
        """
        Take a screenshot of the screen or a specific region.
        
        Args:
            region: Dict with keys 'top', 'left', 'width', 'height' for specific region
            
        Returns:
            Screenshot as numpy array
        """
        if region:
            screenshot = self.screen_capture.grab(region)
        else:
            screenshot = self.screen_capture.grab(self.screen_capture.monitors[1])
        
        # Convert to numpy array
        img = np.array(screenshot)
        return cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)
    
    def save_screenshot(self, filename: str, region: Optional[Dict[str, int]] = None) -> str:
        """Save screenshot to file."""
        img = self.take_screenshot(region)
        cv2.imwrite(filename, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
        return filename
    
    def move_mouse(self, x: int, y: int, duration: float = 0.5) -> bool:
        """
        Move mouse to specific coordinates.
        
        Args:
            x, y: Target coordinates
            duration: Time to take for movement
            
        Returns:
            True if successful
        """
        try:
            pyautogui.moveTo(x, y, duration=duration)
            logger.info(f"Mouse moved to ({x}, {y})")
            return True
        except Exception as e:
            logger.error(f"Failed to move mouse: {e}")
            return False
    
    def click(self, x: Optional[int] = None, y: Optional[int] = None, 
              button: str = 'left', clicks: int = 1, interval: float = 0.1) -> bool:
        """
        Click at specific coordinates or current position.
        
        Args:
            x, y: Click coordinates (None for current position)
            button: 'left', 'right', or 'middle'
            clicks: Number of clicks
            interval: Interval between clicks
            
        Returns:
            True if successful
        """
        try:
            if x is not None and y is not None:
                pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)
                logger.info(f"Clicked at ({x}, {y}) with {button} button")
            else:
                pyautogui.click(clicks=clicks, interval=interval, button=button)
                logger.info(f"Clicked at current position with {button} button")
            return True
        except Exception as e:
            logger.error(f"Failed to click: {e}")
            return False
    
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 1.0, button: str = 'left') -> bool:
        """
        Drag from start to end coordinates.
        
        Args:
            start_x, start_y: Starting coordinates
            end_x, end_y: Ending coordinates
            duration: Time to take for drag
            button: Mouse button to use
            
        Returns:
            True if successful
        """
        try:
            pyautogui.drag(end_x - start_x, end_y - start_y, 
                          duration=duration, button=button)
            logger.info(f"Dragged from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            return True
        except Exception as e:
            logger.error(f"Failed to drag: {e}")
            return False
    
    def scroll(self, clicks: int, x: Optional[int] = None, y: Optional[int] = None) -> bool:
        """
        Scroll at specific position or current position.
        
        Args:
            clicks: Number of scroll clicks (positive = up, negative = down)
            x, y: Position to scroll at (None for current position)
            
        Returns:
            True if successful
        """
        try:
            if x is not None and y is not None:
                pyautogui.scroll(clicks, x=x, y=y)
            else:
                pyautogui.scroll(clicks)
            logger.info(f"Scrolled {clicks} clicks")
            return True
        except Exception as e:
            logger.error(f"Failed to scroll: {e}")
            return False
    
    def type_text(self, text: str, interval: float = 0.05) -> bool:
        """
        Type text with specified interval between characters.
        
        Args:
            text: Text to type
            interval: Interval between keystrokes
            
        Returns:
            True if successful
        """
        try:
            pyautogui.write(text, interval=interval)
            logger.info(f"Typed text: {text[:50]}...")
            return True
        except Exception as e:
            logger.error(f"Failed to type text: {e}")
            return False
    
    def press_key(self, key: str, presses: int = 1, interval: float = 0.1) -> bool:
        """
        Press a key or key combination.
        
        Args:
            key: Key name (e.g., 'enter', 'ctrl+c', 'alt+tab')
            presses: Number of times to press
            interval: Interval between presses
            
        Returns:
            True if successful
        """
        try:
            if '+' in key:
                # Handle key combinations
                keys = key.split('+')
                pyautogui.hotkey(*keys)
                logger.info(f"Pressed key combination: {key}")
            else:
                # Handle single key
                pyautogui.press(key, presses=presses, interval=interval)
                logger.info(f"Pressed key: {key} ({presses} times)")
            return True
        except Exception as e:
            logger.error(f"Failed to press key: {e}")
            return False
    
    def find_image_on_screen(self, template_path: str, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Find an image template on the screen.
        
        Args:
            template_path: Path to template image
            confidence: Matching confidence (0.0 to 1.0)
            
        Returns:
            (x, y) coordinates of match center, or None if not found
        """
        try:
            location = pyautogui.locateOnScreen(template_path, confidence=confidence)
            if location:
                center = pyautogui.center(location)
                logger.info(f"Found image at {center}")
                return center
            else:
                logger.info("Image not found on screen")
                return None
        except Exception as e:
            logger.error(f"Failed to find image: {e}")
            return None
    
    def wait_for_image(self, template_path: str, timeout: float = 10.0, 
                      confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Wait for an image to appear on screen.
        
        Args:
            template_path: Path to template image
            timeout: Maximum time to wait in seconds
            confidence: Matching confidence
            
        Returns:
            (x, y) coordinates of match center, or None if timeout
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            location = self.find_image_on_screen(template_path, confidence)
            if location:
                return location
            time.sleep(0.5)
        
        logger.warning(f"Image not found within {timeout} seconds")
        return None
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position."""
        return pyautogui.position()
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """Get RGB color of pixel at coordinates."""
        return pyautogui.pixel(x, y)
