from setuptools import setup, find_packages

setup(
    name="desktop-automation-agent",
    version="1.0.0",
    description="Comprehensive desktop and browser automation using <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and Gemini 2.5 Flash",
    author="Desktop Automation Team",
    packages=find_packages(),
    python_requires=">=3.8",
    install_requires=[
        "langchain>=0.1.0",
        "langchain-community>=0.0.10",
        "langchain-google-genai>=0.0.6",
        "google-generativeai>=0.3.2",
        "playwright>=1.40.0",
        "pyautogui>=0.9.54",
        "pynput>=1.7.6",
        "pillow>=10.1.0",
        "opencv-python>=********",
        "mss>=9.0.1",
        "numpy>=1.24.3",
        "python-dotenv>=1.0.0",
        "pydantic>=2.5.2",
        "typing-extensions>=4.8.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "black>=23.11.0",
            "flake8>=6.1.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "desktop-agent=src.main:main",
        ],
    },
)
