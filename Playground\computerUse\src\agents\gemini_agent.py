"""
Gemini-powered LangChain agent for desktop and browser automation.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from langchain.agents import Agent<PERSON>xecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain_google_genai import Chat<PERSON><PERSON><PERSON>Gener<PERSON>AI
from langchain.memory import ConversationBufferWindowMemory
from langchain.tools import BaseTool

from ..tools.langchain_tools import get_all_tools, get_desktop_tools, get_browser_tools

logger = logging.getLogger(__name__)


class DesktopAutomationAgent:
    """
    LangChain agent powered by Gemini 2.5 Flash for desktop and browser automation.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model_name: str = "gemini-2.5-flash",
        temperature: float = 0.1,
        max_tokens: int = 8192,
        memory_window: int = 10,
        include_desktop_tools: bool = True,
        include_browser_tools: bool = True,
        verbose: bool = True
    ):
        """
        Initialize the automation agent.
        
        Args:
            api_key: Google API key (if None, will use GOOGLE_API_KEY env var)
            model_name: Gemini model name
            temperature: Model temperature
            max_tokens: Maximum tokens for response
            memory_window: Number of conversation turns to remember
            include_desktop_tools: Whether to include desktop automation tools
            include_browser_tools: Whether to include browser automation tools
            verbose: Whether to enable verbose logging
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("Google API key is required. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")
        
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.memory_window = memory_window
        self.verbose = verbose
        
        # Initialize LLM
        self.llm = ChatGoogleGenerativeAI(
            model=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            google_api_key=self.api_key
        )
        
        # Get tools
        self.tools = []
        if include_desktop_tools:
            self.tools.extend(get_desktop_tools())
        if include_browser_tools:
            self.tools.extend(get_browser_tools())
        
        # Initialize memory
        self.memory = ConversationBufferWindowMemory(
            k=self.memory_window,
            memory_key="chat_history",
            return_messages=True
        )
        
        # Create agent
        self.agent_executor = self._create_agent()
        
        logger.info(f"Initialized DesktopAutomationAgent with {len(self.tools)} tools")
    
    def _create_agent(self) -> AgentExecutor:
        """Create the LangChain agent with tools and prompt."""
        
        # Custom prompt for desktop automation
        prompt_template = """
You are an expert desktop and browser automation assistant. You can control both desktop applications and web browsers to help users automate tasks.

Available tools:
{tools}

Desktop Tools:
- move_mouse: Move mouse cursor to specific coordinates
- click_mouse: Click mouse at coordinates or current position
- type_text: Type text using keyboard
- press_key: Press keys or key combinations (ctrl+c, alt+tab, etc.)
- take_screenshot: Capture screen or region
- scroll: Scroll at specific position

Browser Tools:
- navigate_to_url: Navigate to web pages
- click_element: Click elements by CSS selector
- type_in_element: Type text into web form fields
- wait_for_element: Wait for elements to appear
- get_element_text: Extract text from elements
- execute_javascript: Run JavaScript in browser

Guidelines:
1. Always take a screenshot first to understand the current state
2. Be precise with coordinates and selectors
3. Wait for elements to load before interacting
4. Use appropriate delays between actions
5. Provide clear feedback about what you're doing
6. If an action fails, try alternative approaches
7. For browser automation, prefer CSS selectors over coordinates when possible
8. For desktop automation, use coordinates when CSS selectors aren't available

Current conversation:
{chat_history}

User request: {input}

Think step by step about what actions are needed, then execute them systematically.

{agent_scratchpad}
"""
        
        prompt = PromptTemplate(
            template=prompt_template,
            input_variables=["tools", "chat_history", "input", "agent_scratchpad"]
        )
        
        # Create ReAct agent
        agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        # Create agent executor
        agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            memory=self.memory,
            verbose=self.verbose,
            max_iterations=20,
            max_execution_time=300,  # 5 minutes timeout
            handle_parsing_errors=True
        )
        
        return agent_executor
    
    def execute_task(self, task: str) -> Dict[str, Any]:
        """
        Execute an automation task.
        
        Args:
            task: Natural language description of the task
            
        Returns:
            Dictionary with execution results
        """
        try:
            logger.info(f"Executing task: {task}")
            
            # Execute the task
            result = self.agent_executor.invoke({"input": task})
            
            logger.info("Task execution completed")
            return {
                "success": True,
                "result": result.get("output", ""),
                "intermediate_steps": result.get("intermediate_steps", [])
            }
            
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": ""
            }
    
    def add_custom_tool(self, tool: BaseTool):
        """Add a custom tool to the agent."""
        self.tools.append(tool)
        # Recreate agent with new tools
        self.agent_executor = self._create_agent()
        logger.info(f"Added custom tool: {tool.name}")
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names."""
        return [tool.name for tool in self.tools]
    
    def clear_memory(self):
        """Clear the conversation memory."""
        self.memory.clear()
        logger.info("Memory cleared")
    
    def get_memory_summary(self) -> str:
        """Get a summary of the conversation memory."""
        messages = self.memory.chat_memory.messages
        if not messages:
            return "No conversation history"
        
        summary = f"Conversation history ({len(messages)} messages):\n"
        for i, msg in enumerate(messages[-6:]):  # Show last 6 messages
            role = "Human" if msg.type == "human" else "Assistant"
            content = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
            summary += f"{i+1}. {role}: {content}\n"
        
        return summary


class SpecializedAgent:
    """Factory for creating specialized agents for specific tasks."""
    
    @staticmethod
    def create_browser_agent(**kwargs) -> DesktopAutomationAgent:
        """Create an agent specialized for browser automation."""
        return DesktopAutomationAgent(
            include_desktop_tools=False,
            include_browser_tools=True,
            **kwargs
        )
    
    @staticmethod
    def create_desktop_agent(**kwargs) -> DesktopAutomationAgent:
        """Create an agent specialized for desktop automation."""
        return DesktopAutomationAgent(
            include_desktop_tools=True,
            include_browser_tools=False,
            **kwargs
        )
    
    @staticmethod
    def create_hybrid_agent(**kwargs) -> DesktopAutomationAgent:
        """Create an agent with both desktop and browser capabilities."""
        return DesktopAutomationAgent(
            include_desktop_tools=True,
            include_browser_tools=True,
            **kwargs
        )
