"""
Lang<PERSON>hain tool wrappers for desktop and browser automation.
"""

import asyncio
from typing import Optional, Dict, Any, List
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
import json
import logging

from .desktop_automation import DesktopAutomation
from .browser_automation import BrowserAutomation

logger = logging.getLogger(__name__)


# Desktop Automation Tools

class MouseMoveInput(BaseModel):
    x: int = Field(description="X coordinate to move mouse to")
    y: int = Field(description="Y coordinate to move mouse to")
    duration: float = Field(default=0.5, description="Duration of movement in seconds")


class MouseClickInput(BaseModel):
    x: Optional[int] = Field(default=None, description="X coordinate to click (None for current position)")
    y: Optional[int] = Field(default=None, description="Y coordinate to click (None for current position)")
    button: str = Field(default="left", description="Mouse button: 'left', 'right', or 'middle'")
    clicks: int = Field(default=1, description="Number of clicks")


class TypeTextInput(BaseModel):
    text: str = Field(description="Text to type")
    interval: float = Field(default=0.05, description="Interval between keystrokes")


class PressKeyInput(BaseModel):
    key: str = Field(description="Key to press (e.g., 'enter', 'ctrl+c', 'alt+tab')")
    presses: int = Field(default=1, description="Number of times to press")


class ScreenshotInput(BaseModel):
    filename: Optional[str] = Field(default=None, description="Filename to save screenshot")
    region: Optional[Dict[str, int]] = Field(default=None, description="Region dict with top, left, width, height")


class ScrollInput(BaseModel):
    clicks: int = Field(description="Number of scroll clicks (positive=up, negative=down)")
    x: Optional[int] = Field(default=None, description="X coordinate to scroll at")
    y: Optional[int] = Field(default=None, description="Y coordinate to scroll at")


# Desktop Tools

class MouseMoveTool(BaseTool):
    name = "move_mouse"
    description = "Move mouse cursor to specific coordinates on the screen"
    args_schema = MouseMoveInput

    def _run(self, x: int, y: int, duration: float = 0.5) -> str:
        desktop = DesktopAutomation()
        success = desktop.move_mouse(x, y, duration)
        return f"Mouse moved to ({x}, {y}): {'Success' if success else 'Failed'}"


class MouseClickTool(BaseTool):
    name = "click_mouse"
    description = "Click mouse at specific coordinates or current position"
    args_schema = MouseClickInput

    def _run(self, x: Optional[int] = None, y: Optional[int] = None,
             button: str = "left", clicks: int = 1) -> str:
        desktop = DesktopAutomation()
        success = desktop.click(x, y, button, clicks)
        pos = f"({x}, {y})" if x is not None and y is not None else "current position"
        return f"Clicked at {pos} with {button} button: {'Success' if success else 'Failed'}"


class TypeTextTool(BaseTool):
    name = "type_text"
    description = "Type text using the keyboard"
    args_schema = TypeTextInput

    def _run(self, text: str, interval: float = 0.05) -> str:
        desktop = DesktopAutomation()
        success = desktop.type_text(text, interval)
        return f"Typed text: {'Success' if success else 'Failed'}"


class PressKeyTool(BaseTool):
    name = "press_key"
    description = "Press keyboard keys or key combinations (e.g., 'enter', 'ctrl+c', 'alt+tab')"
    args_schema = PressKeyInput

    def _run(self, key: str, presses: int = 1) -> str:
        desktop = DesktopAutomation()
        success = desktop.press_key(key, presses)
        return f"Pressed key '{key}': {'Success' if success else 'Failed'}"


class TakeScreenshotTool(BaseTool):
    name = "take_screenshot"
    description = "Take a screenshot of the screen or specific region"
    args_schema = ScreenshotInput

    def _run(self, filename: Optional[str] = None, region: Optional[Dict[str, int]] = None) -> str:
        desktop = DesktopAutomation()
        if filename:
            result = desktop.save_screenshot(filename, region)
            return f"Screenshot saved to: {result}"
        else:
            # Take screenshot and return info
            img = desktop.take_screenshot(region)
            return f"Screenshot taken: {img.shape[1]}x{img.shape[0]} pixels"


class ScrollTool(BaseTool):
    name = "scroll"
    description = "Scroll at specific position or current mouse position"
    args_schema = ScrollInput

    def _run(self, clicks: int, x: Optional[int] = None, y: Optional[int] = None) -> str:
        desktop = DesktopAutomation()
        success = desktop.scroll(clicks, x, y)
        direction = "up" if clicks > 0 else "down"
        return f"Scrolled {direction} {abs(clicks)} clicks: {'Success' if success else 'Failed'}"


# Browser Automation Tools

class NavigateInput(BaseModel):
    url: str = Field(description="URL to navigate to")
    wait_until: str = Field(default="networkidle", description="When to consider navigation complete")


class ClickElementInput(BaseModel):
    selector: str = Field(description="CSS selector or text selector for element to click")
    timeout: float = Field(default=30000, description="Timeout in milliseconds")


class TypeInElementInput(BaseModel):
    selector: str = Field(description="CSS selector for input element")
    text: str = Field(description="Text to type")
    clear: bool = Field(default=True, description="Whether to clear existing text first")


class WaitForElementInput(BaseModel):
    selector: str = Field(description="CSS selector to wait for")
    timeout: float = Field(default=30000, description="Timeout in milliseconds")
    state: str = Field(default="visible", description="Element state: 'visible', 'hidden', 'attached', 'detached'")


class GetTextInput(BaseModel):
    selector: str = Field(description="CSS selector for element to get text from")


class ExecuteJSInput(BaseModel):
    script: str = Field(description="JavaScript code to execute")


# Browser Tools

class NavigateTool(BaseTool):
    name = "navigate_to_url"
    description = "Navigate browser to a specific URL"
    args_schema = NavigateInput

    def _run(self, url: str, wait_until: str = "networkidle") -> str:
        try:
            browser = BrowserAutomation()
            result = asyncio.run(browser.navigate_to(url, wait_until))
            return f"Navigated to {url}: {'Success' if result else 'Failed'}"
        except Exception as e:
            return f"Navigation failed: {str(e)}"


class ClickElementTool(BaseTool):
    name = "click_element"
    description = "Click an element in the browser by CSS selector"
    args_schema = ClickElementInput

    def _run(self, selector: str, timeout: float = 30000) -> str:
        try:
            browser = BrowserAutomation()
            result = asyncio.run(browser.click_element(selector, timeout))
            return f"Clicked element '{selector}': {'Success' if result else 'Failed'}"
        except Exception as e:
            return f"Click failed: {str(e)}"


class TypeInElementTool(BaseTool):
    name = "type_in_element"
    description = "Type text into a browser element (input, textarea, etc.)"
    args_schema = TypeInElementInput

    def _run(self, selector: str, text: str, clear: bool = True) -> str:
        try:
            browser = BrowserAutomation()
            result = asyncio.run(browser.type_text(selector, text, clear))
            return f"Typed into '{selector}': {'Success' if result else 'Failed'}"
        except Exception as e:
            return f"Type failed: {str(e)}"


class WaitForElementTool(BaseTool):
    name = "wait_for_element"
    description = "Wait for an element to appear in the browser"
    args_schema = WaitForElementInput

    def _run(self, selector: str, timeout: float = 30000, state: str = "visible") -> str:
        try:
            browser = BrowserAutomation()
            result = asyncio.run(browser.wait_for_element(selector, timeout, state))
            return f"Element '{selector}' found: {'Success' if result else 'Failed'}"
        except Exception as e:
            return f"Wait failed: {str(e)}"


class GetElementTextTool(BaseTool):
    name = "get_element_text"
    description = "Get text content from a browser element"
    args_schema = GetTextInput

    def _run(self, selector: str) -> str:
        try:
            browser = BrowserAutomation()
            text = asyncio.run(browser.get_text(selector))
            return f"Text from '{selector}': {text if text else 'No text found'}"
        except Exception as e:
            return f"Get text failed: {str(e)}"


class ExecuteJavaScriptTool(BaseTool):
    name = "execute_javascript"
    description = "Execute JavaScript code in the browser"
    args_schema = ExecuteJSInput

    def _run(self, script: str) -> str:
        try:
            browser = BrowserAutomation()
            result = asyncio.run(browser.execute_javascript(script))
            return f"JavaScript result: {json.dumps(result) if result is not None else 'No result'}"
        except Exception as e:
            return f"JavaScript execution failed: {str(e)}"


# Tool factory functions

def get_desktop_tools() -> List[BaseTool]:
    """Get all desktop automation tools."""
    return [
        MouseMoveTool(),
        MouseClickTool(),
        TypeTextTool(),
        PressKeyTool(),
        TakeScreenshotTool(),
        ScrollTool(),
    ]


def get_browser_tools() -> List[BaseTool]:
    """Get all browser automation tools."""
    return [
        NavigateTool(),
        ClickElementTool(),
        TypeInElementTool(),
        WaitForElementTool(),
        GetElementTextTool(),
        ExecuteJavaScriptTool(),
    ]


def get_all_tools() -> List[BaseTool]:
    """Get all automation tools."""
    return get_desktop_tools() + get_browser_tools()
