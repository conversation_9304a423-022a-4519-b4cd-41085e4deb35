"""
Basic usage examples for the Desktop Automation Agent.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.gemini_agent import DesktopAutomationAgent, SpecializedAgent

# Load environment variables
load_dotenv()


async def basic_desktop_automation():
    """Example of basic desktop automation tasks."""
    print("🖥️ Basic Desktop Automation Example")
    print("=" * 40)
    
    # Create desktop-only agent
    agent = SpecializedAgent.create_desktop_agent(verbose=True)
    
    tasks = [
        "Take a screenshot of the current screen",
        "Move the mouse to coordinates 500, 300",
        "Click at the current mouse position",
        "Type the text 'Hello, World!'",
        "Press the Enter key",
        "Press Ctrl+A to select all text",
        "Press Ctrl+C to copy the selected text"
    ]
    
    for i, task in enumerate(tasks, 1):
        print(f"\n📋 Task {i}: {task}")
        result = agent.execute_task(task)
        
        if result["success"]:
            print(f"✅ Success: {result['result']}")
        else:
            print(f"❌ Failed: {result['error']}")
        
        # Wait between tasks
        await asyncio.sleep(1)


async def basic_browser_automation():
    """Example of basic browser automation tasks."""
    print("🌐 Basic Browser Automation Example")
    print("=" * 40)
    
    # Create browser-only agent
    agent = SpecializedAgent.create_browser_agent(verbose=True)
    
    tasks = [
        "Navigate to https://www.google.com",
        "Wait for the search input field to appear",
        "Click on the search input field",
        "Type 'desktop automation' in the search field",
        "Press Enter to search",
        "Wait for search results to load",
        "Take a screenshot of the search results"
    ]
    
    for i, task in enumerate(tasks, 1):
        print(f"\n📋 Task {i}: {task}")
        result = agent.execute_task(task)
        
        if result["success"]:
            print(f"✅ Success: {result['result']}")
        else:
            print(f"❌ Failed: {result['error']}")
        
        # Wait between tasks
        await asyncio.sleep(2)


async def hybrid_automation():
    """Example combining desktop and browser automation."""
    print("🔄 Hybrid Automation Example")
    print("=" * 40)
    
    # Create hybrid agent
    agent = SpecializedAgent.create_hybrid_agent(verbose=True)
    
    tasks = [
        "Take a screenshot to see the current desktop",
        "Open a web browser and navigate to https://www.example.com",
        "Take a screenshot of the webpage",
        "Press Alt+Tab to switch between applications",
        "Move mouse to coordinates 100, 100 and click",
        "Go back to the browser and refresh the page",
        "Type some text in any input field on the page"
    ]
    
    for i, task in enumerate(tasks, 1):
        print(f"\n📋 Task {i}: {task}")
        result = agent.execute_task(task)
        
        if result["success"]:
            print(f"✅ Success: {result['result']}")
        else:
            print(f"❌ Failed: {result['error']}")
        
        # Wait between tasks
        await asyncio.sleep(2)


async def complex_workflow():
    """Example of a complex automation workflow."""
    print("⚙️ Complex Workflow Example")
    print("=" * 40)
    
    agent = SpecializedAgent.create_hybrid_agent(verbose=True)
    
    workflow_task = """
    I need you to help me research a topic online and take notes:
    1. First, take a screenshot to see the current state
    2. Open a web browser and go to Wikipedia
    3. Search for 'Artificial Intelligence'
    4. Take a screenshot of the search results
    5. Click on the first article link
    6. Scroll down to read some content
    7. Take another screenshot
    8. Open a text editor (like Notepad)
    9. Type a brief summary of what you learned
    10. Save the file as 'ai_research.txt'
    """
    
    print(f"📋 Complex Task: {workflow_task}")
    result = agent.execute_task(workflow_task)
    
    if result["success"]:
        print(f"✅ Workflow completed: {result['result']}")
    else:
        print(f"❌ Workflow failed: {result['error']}")


def interactive_example():
    """Example of interactive usage."""
    print("💬 Interactive Example")
    print("=" * 40)
    
    agent = SpecializedAgent.create_hybrid_agent(verbose=True)
    
    print("You can now interact with the agent. Type 'quit' to exit.")
    
    while True:
        try:
            user_input = input("\n💬 Enter your automation task: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            result = agent.execute_task(user_input)
            
            if result["success"]:
                print(f"✅ Task completed: {result['result']}")
            else:
                print(f"❌ Task failed: {result['error']}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break


async def main():
    """Main function to run examples."""
    print("🚀 Desktop Automation Agent Examples")
    print("=" * 50)
    
    examples = [
        ("Basic Desktop Automation", basic_desktop_automation),
        ("Basic Browser Automation", basic_browser_automation),
        ("Hybrid Automation", hybrid_automation),
        ("Complex Workflow", complex_workflow),
        ("Interactive Mode", lambda: interactive_example())
    ]
    
    print("Available examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    
    try:
        choice = int(input("\nSelect example to run (1-5): "))
        if 1 <= choice <= len(examples):
            name, func = examples[choice - 1]
            print(f"\n🏃 Running: {name}")
            
            if asyncio.iscoroutinefunction(func):
                await func()
            else:
                func()
        else:
            print("Invalid choice")
    except ValueError:
        print("Please enter a valid number")
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    # Check if API key is set
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ Please set your GOOGLE_API_KEY environment variable")
        print("You can copy .env.example to .env and add your API key")
        sys.exit(1)
    
    # Run examples
    asyncio.run(main())
