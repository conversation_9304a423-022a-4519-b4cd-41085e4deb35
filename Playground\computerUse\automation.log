2025-07-22 17:33:08,384 - __main__ - INFO - Desktop Automation App initialized
2025-07-22 17:33:08,387 - src.agents.gemini_agent - INFO - Initialized DesktopAutomationAgent with 12 tools
2025-07-22 17:33:08,387 - __main__ - INFO - Initialized hybrid agent with 12 tools
2025-07-22 17:33:31,007 - src.agents.gemini_agent - INFO - Executing task: 1. Open a browser and go to Wikipedia
2025-07-22 17:33:37,466 - src.tools.browser_automation - INFO - Started chromium browser
2025-07-22 17:33:38,710 - src.tools.browser_automation - INFO - Navigated to: https://www.wikipedia.org
2025-07-22 17:33:39,608 - src.agents.gemini_agent - INFO - Task execution completed
2025-07-22 17:33:39,656 - src.agents.gemini_agent - INFO - Executing task: 2. Search for 'Machine Learning'
2025-07-22 17:33:43,040 - src.agents.gemini_agent - ERROR - Task execution failed: TypeInElementTool._run() missing 1 required positional argument: 'text'
2025-07-22 17:33:43,059 - src.agents.gemini_agent - INFO - Executing task: 3. Read the first paragraph
2025-07-22 17:33:46,800 - src.tools.browser_automation - ERROR - Failed to get text from #mw-content-text > div.mw-parser-output > p:nth-of-type(1): Browser not started
2025-07-22 17:33:49,194 - src.tools.browser_automation - ERROR - Failed to get text from #mw-content-text p:first-of-type: Browser not started
2025-07-22 17:33:50,591 - src.tools.browser_automation - ERROR - Failed to get text from #mw-content-text > div.mw-parser-output > p:nth-child(2): Browser not started
2025-07-22 17:33:52,212 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 7
}
].
2025-07-22 17:33:54,545 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 4.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 4
}
].
2025-07-22 17:33:58,870 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 8.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
}
].
2025-07-22 17:34:12,236 - src.tools.browser_automation - ERROR - Failed to get text from div.mw-parser-output > p:first-of-type: Browser not started
2025-07-22 17:34:12,567 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
].
2025-07-22 17:34:14,894 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 4.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 44
}
].
2025-07-22 17:34:19,233 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 8.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 10
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 40
}
].
2025-07-22 17:36:38,168 - __main__ - INFO - Desktop Automation App initialized
2025-07-22 17:36:38,170 - src.agents.gemini_agent - INFO - Initialized DesktopAutomationAgent with 12 tools
2025-07-22 17:36:38,171 - __main__ - INFO - Initialized hybrid agent with 12 tools
2025-07-22 17:36:38,171 - src.agents.gemini_agent - INFO - Executing task: 
    Research a topic online and create notes:
        1. Open a browser and go to Wikipedia
        2. Search for 'Machine Learning'
        3. Read the first paragraph
        4. Open a text editor
        5. Write a summary of what you learned
        6. Save the file as 'ml_notes.txt'
    
2025-07-22 17:36:44,549 - src.tools.browser_automation - INFO - Started chromium browser
2025-07-22 17:36:45,702 - src.tools.browser_automation - INFO - Navigated to: https://www.wikipedia.org/
2025-07-22 17:36:48,210 - src.agents.gemini_agent - ERROR - Task execution failed: TypeInElementTool._run() missing 1 required positional argument: 'text'
2025-07-22 17:39:46,675 - __main__ - INFO - Desktop Automation App initialized
2025-07-22 17:39:46,679 - src.agents.gemini_agent - INFO - Initialized DesktopAutomationAgent with 12 tools
2025-07-22 17:39:46,679 - __main__ - INFO - Initialized hybrid agent with 12 tools
2025-07-22 17:39:46,679 - src.agents.gemini_agent - INFO - Executing task: 
    Research a topic online and create notes:
        1. Open a browser and go to Wikipedia
        2. Search for 'Machine Learning'
        3. Read the first paragraph
        4. Open a text editor
        5. Write a summary of what you learned
        6. Save the file as 'ml_notes.txt'
    
2025-07-22 17:39:56,111 - src.tools.browser_automation - INFO - Started chromium browser
2025-07-22 17:39:57,283 - src.tools.browser_automation - INFO - Navigated to: https://www.wikipedia.org/
2025-07-22 17:40:00,592 - src.agents.gemini_agent - ERROR - Task execution failed: TypeInElementTool._run() missing 1 required positional argument: 'text'
