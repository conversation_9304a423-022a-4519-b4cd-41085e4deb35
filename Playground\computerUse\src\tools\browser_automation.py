"""
Browser automation tools using Playwright.
"""

import asyncio
from typing import Optional, List, Dict, Any, Union
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
import logging
import json
import base64

logger = logging.getLogger(__name__)


class BrowserAutomation:
    """Playwright-based browser automation."""
    
    def __init__(self, headless: bool = False, browser_type: str = "chromium"):
        self.headless = headless
        self.browser_type = browser_type
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
    async def start_browser(self, **kwargs) -> bool:
        """
        Start the browser instance.
        
        Args:
            **kwargs: Additional browser launch options
            
        Returns:
            True if successful
        """
        try:
            self.playwright = await async_playwright().start()
            
            # Get browser launcher
            if self.browser_type == "chromium":
                launcher = self.playwright.chromium
            elif self.browser_type == "firefox":
                launcher = self.playwright.firefox
            elif self.browser_type == "webkit":
                launcher = self.playwright.webkit
            else:
                raise ValueError(f"Unsupported browser type: {self.browser_type}")
            
            # Launch browser
            self.browser = await launcher.launch(headless=self.headless, **kwargs)
            self.context = await self.browser.new_context()
            self.page = await self.context.new_page()
            
            logger.info(f"Started {self.browser_type} browser")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            return False
    
    async def close_browser(self) -> bool:
        """Close the browser instance."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            
            logger.info("Browser closed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to close browser: {e}")
            return False
    
    async def navigate_to(self, url: str, wait_until: str = "networkidle") -> bool:
        """
        Navigate to a URL.
        
        Args:
            url: Target URL
            wait_until: When to consider navigation complete
            
        Returns:
            True if successful
        """
        try:
            if not self.page:
                await self.start_browser()
            
            await self.page.goto(url, wait_until=wait_until)
            logger.info(f"Navigated to: {url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to navigate to {url}: {e}")
            return False
    
    async def click_element(self, selector: str, timeout: float = 30000) -> bool:
        """
        Click an element by selector.
        
        Args:
            selector: CSS selector or text selector
            timeout: Timeout in milliseconds
            
        Returns:
            True if successful
        """
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            await self.page.click(selector, timeout=timeout)
            logger.info(f"Clicked element: {selector}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to click element {selector}: {e}")
            return False
    
    async def type_text(self, selector: str, text: str, clear: bool = True, 
                       timeout: float = 30000) -> bool:
        """
        Type text into an element.
        
        Args:
            selector: CSS selector for input element
            text: Text to type
            clear: Whether to clear existing text first
            timeout: Timeout in milliseconds
            
        Returns:
            True if successful
        """
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            if clear:
                await self.page.fill(selector, text, timeout=timeout)
            else:
                await self.page.type(selector, text, timeout=timeout)
            
            logger.info(f"Typed text into {selector}: {text[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to type text into {selector}: {e}")
            return False
    
    async def press_key(self, key: str, selector: Optional[str] = None) -> bool:
        """
        Press a key, optionally on a specific element.
        
        Args:
            key: Key to press (e.g., 'Enter', 'Escape', 'Tab')
            selector: Optional element selector to focus first
            
        Returns:
            True if successful
        """
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            if selector:
                await self.page.press(selector, key)
            else:
                await self.page.keyboard.press(key)
            
            logger.info(f"Pressed key: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to press key {key}: {e}")
            return False
    
    async def scroll_page(self, direction: str = "down", amount: int = 3) -> bool:
        """
        Scroll the page.
        
        Args:
            direction: 'up', 'down', 'left', 'right'
            amount: Number of scroll steps
            
        Returns:
            True if successful
        """
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            for _ in range(amount):
                if direction == "down":
                    await self.page.keyboard.press("PageDown")
                elif direction == "up":
                    await self.page.keyboard.press("PageUp")
                elif direction == "left":
                    await self.page.keyboard.press("ArrowLeft")
                elif direction == "right":
                    await self.page.keyboard.press("ArrowRight")
            
            logger.info(f"Scrolled {direction} {amount} times")
            return True
            
        except Exception as e:
            logger.error(f"Failed to scroll: {e}")
            return False
    
    async def wait_for_element(self, selector: str, timeout: float = 30000, 
                              state: str = "visible") -> bool:
        """
        Wait for an element to appear.
        
        Args:
            selector: CSS selector
            timeout: Timeout in milliseconds
            state: Element state to wait for ('visible', 'hidden', 'attached', 'detached')
            
        Returns:
            True if element appears
        """
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            await self.page.wait_for_selector(selector, timeout=timeout, state=state)
            logger.info(f"Element found: {selector}")
            return True
            
        except Exception as e:
            logger.error(f"Element not found {selector}: {e}")
            return False
    
    async def get_text(self, selector: str) -> Optional[str]:
        """Get text content of an element."""
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            text = await self.page.text_content(selector)
            logger.info(f"Got text from {selector}: {text[:100] if text else 'None'}...")
            return text
            
        except Exception as e:
            logger.error(f"Failed to get text from {selector}: {e}")
            return None
    
    async def get_attribute(self, selector: str, attribute: str) -> Optional[str]:
        """Get attribute value of an element."""
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            value = await self.page.get_attribute(selector, attribute)
            logger.info(f"Got attribute {attribute} from {selector}: {value}")
            return value
            
        except Exception as e:
            logger.error(f"Failed to get attribute {attribute} from {selector}: {e}")
            return None
    
    async def take_screenshot(self, filename: Optional[str] = None, 
                             full_page: bool = False) -> Optional[str]:
        """
        Take a screenshot of the page.
        
        Args:
            filename: Optional filename to save to
            full_page: Whether to capture full page or just viewport
            
        Returns:
            Base64 encoded screenshot or filename if saved
        """
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            screenshot = await self.page.screenshot(
                path=filename,
                full_page=full_page
            )
            
            if filename:
                logger.info(f"Screenshot saved to: {filename}")
                return filename
            else:
                # Return base64 encoded screenshot
                return base64.b64encode(screenshot).decode()
                
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            return None
    
    async def execute_javascript(self, script: str) -> Any:
        """Execute JavaScript in the page."""
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            result = await self.page.evaluate(script)
            logger.info(f"Executed JavaScript: {script[:100]}...")
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute JavaScript: {e}")
            return None
    
    async def get_page_info(self) -> Dict[str, Any]:
        """Get current page information."""
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            info = {
                "url": self.page.url,
                "title": await self.page.title(),
                "viewport": self.page.viewport_size
            }
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get page info: {e}")
            return {}
    
    async def find_elements(self, selector: str) -> List[str]:
        """Find all elements matching selector and return their text content."""
        try:
            if not self.page:
                raise Exception("Browser not started")
            
            elements = await self.page.query_selector_all(selector)
            texts = []
            
            for element in elements:
                text = await element.text_content()
                if text:
                    texts.append(text.strip())
            
            logger.info(f"Found {len(texts)} elements matching {selector}")
            return texts
            
        except Exception as e:
            logger.error(f"Failed to find elements {selector}: {e}")
            return []
