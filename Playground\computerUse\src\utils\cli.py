"""
Command-line interface utilities.
"""

import os
import sys
from typing import List, Dict, Any
import readline  # For better input handling


class CLIInterface:
    """Enhanced CLI interface with history and auto-completion."""
    
    def __init__(self):
        self.history_file = os.path.expanduser("~/.automation_history")
        self.commands = [
            "help", "status", "tools", "memory", "clear", "quit", "exit",
            "init hybrid", "init desktop", "init browser",
            "take screenshot", "move mouse", "click", "type", "press key",
            "navigate to", "click element", "wait for element"
        ]
        self.setup_readline()
    
    def setup_readline(self):
        """Setup readline for better input experience."""
        try:
            # Load history
            if os.path.exists(self.history_file):
                readline.read_history_file(self.history_file)
            
            # Set history length
            readline.set_history_length(1000)
            
            # Setup auto-completion
            readline.set_completer(self.completer)
            readline.parse_and_bind("tab: complete")
            
        except ImportError:
            # readline not available on all systems
            pass
    
    def completer(self, text: str, state: int):
        """Auto-completion function."""
        matches = [cmd for cmd in self.commands if cmd.startswith(text)]
        try:
            return matches[state]
        except IndexError:
            return None
    
    def save_history(self):
        """Save command history."""
        try:
            readline.write_history_file(self.history_file)
        except (ImportError, NameError):
            pass
    
    def print_banner(self):
        """Print application banner."""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                Desktop Automation Agent                      ║
║              Powered by Gemini 2.5 Flash                    ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def print_colored(self, text: str, color: str = "white"):
        """Print colored text (basic implementation)."""
        colors = {
            "red": "\033[91m",
            "green": "\033[92m",
            "yellow": "\033[93m",
            "blue": "\033[94m",
            "purple": "\033[95m",
            "cyan": "\033[96m",
            "white": "\033[97m",
            "reset": "\033[0m"
        }
        
        if color in colors:
            print(f"{colors[color]}{text}{colors['reset']}")
        else:
            print(text)
    
    def confirm_action(self, message: str) -> bool:
        """Ask for user confirmation."""
        while True:
            response = input(f"{message} (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                print("Please enter 'y' or 'n'")
    
    def select_option(self, options: List[str], prompt: str = "Select an option") -> int:
        """Present options and get user selection."""
        print(f"\n{prompt}:")
        for i, option in enumerate(options, 1):
            print(f"  {i}. {option}")
        
        while True:
            try:
                choice = int(input("\nEnter choice number: "))
                if 1 <= choice <= len(options):
                    return choice - 1
                else:
                    print(f"Please enter a number between 1 and {len(options)}")
            except ValueError:
                print("Please enter a valid number")
    
    def display_table(self, data: List[Dict[str, Any]], headers: List[str]):
        """Display data in a simple table format."""
        if not data:
            print("No data to display")
            return
        
        # Calculate column widths
        widths = {}
        for header in headers:
            widths[header] = len(header)
            for row in data:
                if header in row:
                    widths[header] = max(widths[header], len(str(row[header])))
        
        # Print header
        header_row = " | ".join(header.ljust(widths[header]) for header in headers)
        print(header_row)
        print("-" * len(header_row))
        
        # Print data rows
        for row in data:
            data_row = " | ".join(str(row.get(header, "")).ljust(widths[header]) for header in headers)
            print(data_row)
    
    def progress_bar(self, current: int, total: int, width: int = 50):
        """Display a progress bar."""
        percent = current / total
        filled = int(width * percent)
        bar = "█" * filled + "░" * (width - filled)
        print(f"\rProgress: [{bar}] {percent:.1%} ({current}/{total})", end="", flush=True)
        
        if current == total:
            print()  # New line when complete
