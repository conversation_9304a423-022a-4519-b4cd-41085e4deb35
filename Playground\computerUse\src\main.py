"""
Main application interface for the Desktop Automation Agent.
"""

import os
import sys
import argparse
import logging
from typing import Optional
from dotenv import load_dotenv

from .agents.gemini_agent import DesktopAutomationAgent, SpecializedAgent
from .utils.config import setup_logging, load_config
from .utils.cli import CLIInterface

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class DesktopAutomationApp:
    """Main application class for desktop automation."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the application."""
        self.config = load_config(config_path)
        setup_logging(self.config.get("log_level", "INFO"))
        
        self.agent: Optional[DesktopAutomationAgent] = None
        self.cli = CLIInterface()
        
        logger.info("Desktop Automation App initialized")
    
    def initialize_agent(self, agent_type: str = "hybrid", **kwargs):
        """
        Initialize the automation agent.
        
        Args:
            agent_type: Type of agent ('hybrid', 'desktop', 'browser')
            **kwargs: Additional agent configuration
        """
        try:
            # Merge config with kwargs
            agent_config = {**self.config.get("agent", {}), **kwargs}
            
            if agent_type == "hybrid":
                self.agent = SpecializedAgent.create_hybrid_agent(**agent_config)
            elif agent_type == "desktop":
                self.agent = SpecializedAgent.create_desktop_agent(**agent_config)
            elif agent_type == "browser":
                self.agent = SpecializedAgent.create_browser_agent(**agent_config)
            else:
                raise ValueError(f"Unknown agent type: {agent_type}")
            
            logger.info(f"Initialized {agent_type} agent with {len(self.agent.tools)} tools")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize agent: {e}")
            return False
    
    def execute_task(self, task: str) -> bool:
        """Execute a single automation task."""
        if not self.agent:
            print("❌ Agent not initialized. Please initialize first.")
            return False
        
        print(f"🤖 Executing task: {task}")
        print("=" * 50)
        
        result = self.agent.execute_task(task)
        
        if result["success"]:
            print("✅ Task completed successfully!")
            print(f"Result: {result['result']}")
        else:
            print("❌ Task failed!")
            print(f"Error: {result['error']}")
        
        print("=" * 50)
        return result["success"]
    
    def interactive_mode(self):
        """Run the application in interactive mode."""
        print("🚀 Desktop Automation Agent - Interactive Mode")
        print("Type 'help' for commands, 'quit' to exit")
        print("=" * 50)
        
        while True:
            try:
                user_input = input("\n💬 Enter command or task: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'help':
                    self.show_help()
                elif user_input.lower() == 'status':
                    self.show_status()
                elif user_input.lower() == 'tools':
                    self.show_tools()
                elif user_input.lower() == 'memory':
                    self.show_memory()
                elif user_input.lower() == 'clear':
                    self.clear_memory()
                elif user_input.startswith('init '):
                    agent_type = user_input[5:].strip()
                    self.initialize_agent(agent_type)
                else:
                    # Execute as automation task
                    self.execute_task(user_input)
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                logger.error(f"Error in interactive mode: {e}")
                print(f"❌ Error: {e}")
    
    def batch_mode(self, tasks_file: str):
        """Run tasks from a file in batch mode."""
        try:
            with open(tasks_file, 'r') as f:
                tasks = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            
            print(f"🚀 Running {len(tasks)} tasks from {tasks_file}")
            
            successful = 0
            for i, task in enumerate(tasks, 1):
                print(f"\n📋 Task {i}/{len(tasks)}: {task}")
                if self.execute_task(task):
                    successful += 1
            
            print(f"\n📊 Batch completed: {successful}/{len(tasks)} tasks successful")
            
        except FileNotFoundError:
            print(f"❌ Tasks file not found: {tasks_file}")
        except Exception as e:
            logger.error(f"Error in batch mode: {e}")
            print(f"❌ Batch mode error: {e}")
    
    def show_help(self):
        """Show help information."""
        help_text = """
🔧 Available Commands:
  help          - Show this help message
  status        - Show agent status
  tools         - List available tools
  memory        - Show conversation memory
  clear         - Clear conversation memory
  init <type>   - Initialize agent (hybrid/desktop/browser)
  quit/exit/q   - Exit the application

🤖 Agent Types:
  hybrid        - Both desktop and browser automation (default)
  desktop       - Desktop-only automation
  browser       - Browser-only automation

💡 Example Tasks:
  "Take a screenshot of the current screen"
  "Open Google Chrome and navigate to google.com"
  "Click on the search box and type 'hello world'"
  "Press Ctrl+C to copy selected text"
  "Move mouse to coordinates 100, 200 and click"
        """
        print(help_text)
    
    def show_status(self):
        """Show current agent status."""
        if self.agent:
            print(f"✅ Agent Status: Active")
            print(f"🔧 Available Tools: {len(self.agent.tools)}")
            print(f"💭 Memory: {len(self.agent.memory.chat_memory.messages)} messages")
        else:
            print("❌ Agent Status: Not initialized")
    
    def show_tools(self):
        """Show available tools."""
        if self.agent:
            tools = self.agent.get_available_tools()
            print(f"🔧 Available Tools ({len(tools)}):")
            for tool in tools:
                print(f"  • {tool}")
        else:
            print("❌ No agent initialized")
    
    def show_memory(self):
        """Show conversation memory."""
        if self.agent:
            memory_summary = self.agent.get_memory_summary()
            print(f"💭 {memory_summary}")
        else:
            print("❌ No agent initialized")
    
    def clear_memory(self):
        """Clear conversation memory."""
        if self.agent:
            self.agent.clear_memory()
            print("🧹 Memory cleared")
        else:
            print("❌ No agent initialized")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Desktop Automation Agent")
    parser.add_argument("--config", "-c", help="Configuration file path")
    parser.add_argument("--agent-type", "-a", choices=["hybrid", "desktop", "browser"], 
                       default="hybrid", help="Agent type")
    parser.add_argument("--batch", "-b", help="Run tasks from file in batch mode")
    parser.add_argument("--task", "-t", help="Execute a single task and exit")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Initialize app
    app = DesktopAutomationApp(args.config)
    
    # Initialize agent
    agent_config = {"verbose": args.verbose} if args.verbose else {}
    if not app.initialize_agent(args.agent_type, **agent_config):
        print("❌ Failed to initialize agent. Check your configuration.")
        sys.exit(1)
    
    # Run based on mode
    if args.batch:
        app.batch_mode(args.batch)
    elif args.task:
        success = app.execute_task(args.task)
        sys.exit(0 if success else 1)
    else:
        app.interactive_mode()


if __name__ == "__main__":
    main()
